version: '3.8'

services:
  # MongoDB Database
  mongodb:
    image: mongo:7.0
    container_name: digital_contract_mongodb
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password123
      MONGO_INITDB_DATABASE: digital_contracts
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
      - ./mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
    networks:
      - digital_contract_network

  # Redis for caching and sessions
  redis:
    image: redis:7.2-alpine
    container_name: digital_contract_redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - digital_contract_network

  # IPFS Node (optional)
  ipfs:
    image: ipfs/kubo:latest
    container_name: digital_contract_ipfs
    restart: unless-stopped
    ports:
      - "4001:4001"
      - "5001:5001"
      - "8080:8080"
    volumes:
      - ipfs_data:/data/ipfs
    networks:
      - digital_contract_network
    environment:
      - IPFS_PROFILE=server

  # Backend API Server
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: digital_contract_backend
    restart: unless-stopped
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=development
      - PORT=3001
      - MONGO_URI=****************************************************************************
      - REDIS_URL=redis://redis:6379
      - SOLANA_CLUSTER=devnet
      - IPFS_HOST=ipfs
      - IPFS_PORT=5001
      - IPFS_PROTOCOL=http
      - PLATFORM_FEE_RECIPIENT_PRIVATE_KEY=[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64]
    depends_on:
      - mongodb
      - redis
      - ipfs
    volumes:
      - ./backend:/app
      - /app/node_modules
    networks:
      - digital_contract_network
    command: npm run dev

  # Frontend Development Server
  frontend:
    build:
      context: .
      dockerfile: Dockerfile.frontend
    container_name: digital_contract_frontend
    restart: unless-stopped
    ports:
      - "5173:5173"
    environment:
      - VITE_API_URL=http://localhost:3001
      - VITE_SOLANA_CLUSTER=devnet
      - VITE_SOLANA_RPC_URL=https://api.devnet.solana.com
    volumes:
      - .:/app
      - /app/node_modules
    networks:
      - digital_contract_network
    command: npm run dev -- --host 0.0.0.0

  # Nginx Reverse Proxy (for production)
  nginx:
    image: nginx:alpine
    container_name: digital_contract_nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - backend
      - frontend
    networks:
      - digital_contract_network
    profiles:
      - production

volumes:
  mongodb_data:
    driver: local
  redis_data:
    driver: local
  ipfs_data:
    driver: local

networks:
  digital_contract_network:
    driver: bridge
