#!/bin/bash

# 🚨 CRITICAL SECURITY FIX: Remove exposed API keys from git history
# This script removes sensitive data from git history and implements proper secrets management

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_critical() {
    echo -e "${RED}[CRITICAL]${NC} $1"
}

# Function to check if git-filter-repo is installed
check_git_filter_repo() {
    if ! command -v git-filter-repo >/dev/null 2>&1; then
        print_error "git-filter-repo is not installed"
        print_status "Installing git-filter-repo..."
        
        if command -v pip3 >/dev/null 2>&1; then
            pip3 install git-filter-repo
        elif command -v pip >/dev/null 2>&1; then
            pip install git-filter-repo
        else
            print_error "Please install git-filter-repo manually:"
            print_status "pip install git-filter-repo"
            exit 1
        fi
    fi
}

# Function to backup the repository
backup_repository() {
    print_status "Creating backup of the repository..."
    
    BACKUP_DIR="../sign-contract-backup-$(date +%Y%m%d-%H%M%S)"
    cp -r . "$BACKUP_DIR"
    
    print_success "Repository backed up to: $BACKUP_DIR"
}

# Function to remove sensitive data from git history
clean_git_history() {
    print_critical "🚨 REMOVING SENSITIVE DATA FROM GIT HISTORY"
    print_warning "This operation will rewrite git history and change all commit hashes!"
    
    read -p "Are you sure you want to continue? (yes/no): " confirm
    if [ "$confirm" != "yes" ]; then
        print_status "Operation cancelled"
        exit 0
    fi
    
    # Create a file with patterns to remove
    cat > /tmp/secrets-to-remove.txt << 'EOF'
re_J8oos3Wp_GPjKaMAtDtbqKZcppayQuxGu
RESEND_API_KEY=re_J8oos3Wp_GPjKaMAtDtbqKZcppayQuxGu
're_J8oos3Wp_GPjKaMAtDtbqKZcppayQuxGu'
"re_J8oos3Wp_GPjKaMAtDtbqKZcppayQuxGu"
EOF

    # Use git-filter-repo to remove sensitive data
    print_status "Removing sensitive data from git history..."
    
    # Remove the exposed API key from all files in history
    git filter-repo --replace-text /tmp/secrets-to-remove.txt --force
    
    # Clean up
    rm /tmp/secrets-to-remove.txt
    
    print_success "Sensitive data removed from git history"
}

# Function to update current files with proper environment variables
fix_current_files() {
    print_status "Fixing current files to use environment variables..."
    
    # Fix backend email service
    if [ -f "backend/src/services/emailService.js" ]; then
        sed -i "s/re_J8oos3Wp_GPjKaMAtDtbqKZcppayQuxGu/process.env.RESEND_API_KEY/g" backend/src/services/emailService.js
        print_success "Fixed backend/src/services/emailService.js"
    fi
    
    # Remove any .env files with exposed keys
    if [ -f ".env" ]; then
        if grep -q "re_J8oos3Wp_GPjKaMAtDtbqKZcppayQuxGu" .env; then
            print_warning "Found exposed API key in .env file"
            sed -i "s/RESEND_API_KEY=re_J8oos3Wp_GPjKaMAtDtbqKZcppayQuxGu/RESEND_API_KEY=your_new_resend_api_key_here/g" .env
            print_success "Updated .env file"
        fi
    fi
    
    if [ -f "backend/.env" ]; then
        if grep -q "re_J8oos3Wp_GPjKaMAtDtbqKZcppayQuxGu" backend/.env; then
            print_warning "Found exposed API key in backend/.env file"
            sed -i "s/RESEND_API_KEY=re_J8oos3Wp_GPjKaMAtDtbqKZcppayQuxGu/RESEND_API_KEY=your_new_resend_api_key_here/g" backend/.env
            print_success "Updated backend/.env file"
        fi
    fi
}

# Function to create proper .gitignore
update_gitignore() {
    print_status "Updating .gitignore to prevent future exposures..."
    
    # Add comprehensive .gitignore rules
    cat >> .gitignore << 'EOF'

# Environment variables and secrets
.env
.env.local
.env.development
.env.production
.env.test
backend/.env
backend/.env.local
backend/.env.development
backend/.env.production
backend/.env.test

# API keys and secrets
**/secrets/
**/*secret*
**/*key*.json
**/*credentials*
**/wallet.json

# Logs that might contain sensitive data
*.log
logs/
**/*.log

# Database files
*.db
*.sqlite
*.sqlite3

# Temporary files
.tmp/
temp/
EOF

    print_success "Updated .gitignore"
}

# Function to create secure environment templates
create_env_templates() {
    print_status "Creating secure environment templates..."
    
    # Create root .env.example
    cat > .env.example << 'EOF'
# Frontend Environment Variables
VITE_APP_NAME=SecureContract Pro
VITE_APP_URL=http://localhost:5173
VITE_API_URL=http://localhost:3001
VITE_SOLANA_CLUSTER=devnet
VITE_GA_TRACKING_ID=your_google_analytics_id_here

# Development settings
NODE_ENV=development
EOF

    # Create backend .env.example
    cat > backend/.env.example << 'EOF'
# Backend Environment Variables

# Server Configuration
NODE_ENV=development
PORT=3001
FRONTEND_URL=http://localhost:5173

# Database Configuration
MONGO_URI=******************************************************************************
REDIS_URL=redis://localhost:6379

# Security
JWT_SECRET=your_jwt_secret_here_minimum_32_characters
SESSION_SECRET=your_session_secret_here_minimum_32_characters

# Email Configuration (Resend)
RESEND_API_KEY=your_new_resend_api_key_here
EMAIL_FROM=<EMAIL>

# Solana Configuration
SOLANA_CLUSTER=devnet
ANCHOR_PROVIDER_URL=https://api.devnet.solana.com
ANCHOR_WALLET=/path/to/your/wallet.json
PLATFORM_FEE_RECIPIENT_PRIVATE_KEY=[your,platform,fee,recipient,private,key,array]

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
EOF

    print_success "Created secure environment templates"
}

# Function to generate new secrets
generate_new_secrets() {
    print_status "Generating new secure secrets..."
    
    # Generate JWT secret
    JWT_SECRET=$(openssl rand -hex 32)
    SESSION_SECRET=$(openssl rand -hex 32)
    
    print_success "Generated new JWT_SECRET: $JWT_SECRET"
    print_success "Generated new SESSION_SECRET: $SESSION_SECRET"
    
    print_warning "Please update your environment files with these new secrets"
}

# Function to create security documentation
create_security_docs() {
    print_status "Creating security documentation..."
    
    cat > SECURITY.md << 'EOF'
# 🔒 Security Guidelines for SecureContract Pro

## 🚨 NEVER COMMIT THESE TO GIT:
- API keys (Resend, Google Analytics, etc.)
- Private keys (Solana wallets, encryption keys)
- Database passwords
- JWT secrets
- Any `.env` files with real values

## ✅ Proper Secrets Management:

### 1. Environment Variables
- Use `.env.example` files as templates
- Copy to `.env` and fill with real values
- Never commit `.env` files

### 2. Production Deployment
- Use platform-specific environment variable settings:
  - Vercel: Project Settings → Environment Variables
  - Netlify: Site Settings → Environment Variables
  - AWS: Parameter Store or Secrets Manager
  - Docker: Use secrets or environment files

### 3. API Key Rotation
- Rotate API keys regularly (monthly recommended)
- Immediately rotate if exposed
- Use different keys for different environments

### 4. Git Security
- Always check commits before pushing
- Use `git diff --cached` to review staged changes
- Consider using pre-commit hooks

## 🔄 If You Accidentally Commit Secrets:

1. **Immediately rotate the exposed secrets**
2. **Remove from git history** (this script helps with that)
3. **Force push the cleaned history**
4. **Notify team members to re-clone the repository**

## 📞 Emergency Response:
If secrets are exposed in a public repository:
1. Rotate ALL exposed secrets immediately
2. Run this security fix script
3. Force push the cleaned repository
4. Monitor for any unauthorized usage

## 🛡️ Best Practices:
- Use different API keys for development and production
- Implement proper access controls
- Regular security audits
- Monitor API usage for anomalies
- Use secrets management services in production
EOF

    print_success "Created SECURITY.md documentation"
}

# Main function
main() {
    print_critical "🚨 SECURITY FIX: Removing Exposed API Keys"
    echo "=============================================="
    
    print_warning "This script will:"
    echo "1. Backup your repository"
    echo "2. Remove sensitive data from git history"
    echo "3. Fix current files"
    echo "4. Update security configurations"
    echo "5. Generate new secrets"
    
    echo ""
    read -p "Continue? (yes/no): " confirm
    if [ "$confirm" != "yes" ]; then
        print_status "Operation cancelled"
        exit 0
    fi
    
    # Execute security fixes
    backup_repository
    check_git_filter_repo
    fix_current_files
    update_gitignore
    create_env_templates
    generate_new_secrets
    create_security_docs
    
    print_critical "⚠️  IMPORTANT NEXT STEPS:"
    echo "1. 🔑 Get a new Resend API key from https://resend.com/api-keys"
    echo "2. 🔄 Update your .env files with the new API key"
    echo "3. 🗑️  Delete the old API key from Resend dashboard"
    echo "4. 🚀 Test the application with new credentials"
    echo "5. 📝 Update any deployment environment variables"
    
    print_warning "Optional: Clean git history (DESTRUCTIVE - changes all commit hashes):"
    read -p "Do you want to clean git history? (yes/no): " clean_history
    if [ "$clean_history" = "yes" ]; then
        clean_git_history
        print_critical "⚠️  Git history cleaned! All commit hashes have changed."
        print_warning "Team members will need to re-clone the repository."
    fi
    
    print_success "🎉 Security fix completed!"
}

# Run the main function
main "$@"
EOF
