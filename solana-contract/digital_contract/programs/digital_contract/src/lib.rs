use anchor_lang::prelude::*;
use anchor_lang::system_program;

declare_id!("Fg6PaFpoGXkYsidMpWTK6W2BeZ7FEfcYkg476zPFsLnS");

const PLATFORM_FEE_LAMPORTS: u64 = 10_000_000; // 0.01 SOL in lamports (1 SOL = 1,000,000,000 lamports)

#[program]
pub mod digital_contract {
    use super::*;

    pub fn create_contract(
        ctx: Context<CreateContract>,
        document_hash: String,
        approver: Pubkey,
    ) -> Result<()> {
        let contract = &mut ctx.accounts.contract;
        contract.creator = ctx.accounts.creator.key();
        contract.approver = approver;
        contract.document_hash = document_hash;
        contract.is_approved = false;

        // Transfer platform fee
        let cpi_context = CpiContext::new(
            ctx.accounts.system_program.to_account_info(),
            system_program::Transfer {
                from: ctx.accounts.creator.to_account_info(),
                to: ctx.accounts.platform_fee_recipient.to_account_info(),
            },
        );
        system_program::transfer(cpi_context, PLATFORM_FEE_LAMPORTS)?;

        Ok(())
    }

    pub fn sign_contract(ctx: Context<SignContract>) -> Result<()> {
        let contract = &mut ctx.accounts.contract;
        require!(
            ctx.accounts.approver.key() == contract.approver,
            ContractError::UnauthorizedSigner
        );
        contract.is_approved = true;
        Ok(())
    }
}

#[derive(Accounts)]
pub struct CreateContract<'info> {
    #[account(init, payer = creator, space = 8 + 32 + 32 + 32 + 1 + 4 + 256)]
    pub contract: Account<'info, Contract>,
    #[account(mut)]
    pub creator: Signer<'info>,
    /// CHECK: This is the platform fee recipient, no constraints needed beyond being a valid account.
    #[account(mut)]
    pub platform_fee_recipient: AccountInfo<'info>,
    pub system_program: Program<'info, System>,
}

#[derive(Accounts)]
pub struct SignContract<'info> {
    #[account(mut, has_one = approver)]
    pub contract: Account<'info, Contract>,
    pub approver: Signer<'info>,
}

#[account]
pub struct Contract {
    pub creator: Pubkey,
    pub approver: Pubkey,
    pub document_hash: String,
    pub is_approved: bool,
}

#[error_code]
pub enum ContractError {
    #[msg("The signer is not the designated approver for this contract.")]
    UnauthorizedSigner,
}

