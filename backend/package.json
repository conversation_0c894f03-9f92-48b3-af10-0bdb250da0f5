{"name": "digital-contract-backend", "version": "1.0.0", "description": "Backend API for Digital Contract Platform on Solana", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint .", "lint:fix": "eslint . --fix", "docker:build": "docker build -t digital-contract-backend .", "docker:run": "docker run -p 3001:3001 digital-contract-backend"}, "keywords": ["solana", "blockchain", "smart-contracts", "digital-signatures"], "author": "Digital Contract Team", "license": "MIT", "dependencies": {"@project-serum/anchor": "^0.26.0", "@solana/web3.js": "^1.98.2", "body-parser": "^1.20.2", "cors": "^2.8.5", "crypto-js": "^4.2.0", "dotenv": "^17.0.0", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "ipfs-http-client": "^56.0.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.16.1", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.7", "pdfkit": "^0.17.1", "redis": "^4.6.10", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "winston": "^3.11.0"}, "devDependencies": {"eslint": "^8.55.0", "jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.3"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}