version: '3.8'

services:
  # Frontend (React + Nginx)
  frontend:
    build:
      context: .
      dockerfile: Dockerfile.production
    container_name: securecontract_frontend
    restart: unless-stopped
    ports:
      - "80:8080"
      - "443:8080"
    environment:
      - NODE_ENV=production
    networks:
      - securecontract_network
    depends_on:
      - backend
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Backend (Node.js API)
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: securecontract_backend
    restart: unless-stopped
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
      - MONGO_URI=mongodb://admin:${MONGO_PASSWORD}@mongodb:27017/digital_contracts?authSource=admin
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=${JWT_SECRET}
      - SESSION_SECRET=${SESSION_SECRET}
      - RESEND_API_KEY=${RESEND_API_KEY}
      - SOLANA_CLUSTER=mainnet-beta
      - PLATFORM_FEE_RECIPIENT_PRIVATE_KEY=${PLATFORM_FEE_RECIPIENT_PRIVATE_KEY}
    networks:
      - securecontract_network
    depends_on:
      - mongodb
      - redis
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # MongoDB Database
  mongodb:
    image: mongo:6.0
    container_name: securecontract_mongodb
    restart: unless-stopped
    ports:
      - "27017:27017"
    environment:
      - MONGO_INITDB_ROOT_USERNAME=admin
      - MONGO_INITDB_ROOT_PASSWORD=${MONGO_PASSWORD}
      - MONGO_INITDB_DATABASE=digital_contracts
    volumes:
      - mongodb_data:/data/db
      - mongodb_config:/data/configdb
      - ./mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
    networks:
      - securecontract_network
    command: mongod --auth --bind_ip_all
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: securecontract_redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - securecontract_network
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # IPFS for document storage
  ipfs:
    image: ipfs/go-ipfs:latest
    container_name: securecontract_ipfs
    restart: unless-stopped
    ports:
      - "4001:4001"
      - "5001:5001"
      - "8081:8080"
    volumes:
      - ipfs_data:/data/ipfs
    networks:
      - securecontract_network
    environment:
      - IPFS_PROFILE=server
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:5001/api/v0/version"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx Load Balancer (optional, for multiple frontend instances)
  nginx-lb:
    image: nginx:alpine
    container_name: securecontract_nginx_lb
    restart: unless-stopped
    ports:
      - "8080:80"
    volumes:
      - ./nginx-lb.conf:/etc/nginx/nginx.conf:ro
    networks:
      - securecontract_network
    depends_on:
      - frontend
    profiles:
      - load-balancer

  # Monitoring with Prometheus (optional)
  prometheus:
    image: prom/prometheus:latest
    container_name: securecontract_prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    networks:
      - securecontract_network
    profiles:
      - monitoring

  # Grafana for monitoring dashboards (optional)
  grafana:
    image: grafana/grafana:latest
    container_name: securecontract_grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
    volumes:
      - grafana_data:/var/lib/grafana
    networks:
      - securecontract_network
    profiles:
      - monitoring

volumes:
  mongodb_data:
    driver: local
  mongodb_config:
    driver: local
  redis_data:
    driver: local
  ipfs_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  securecontract_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
